// Debug script to test collections functionality
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://nvjzrkjuwsuwmfklpjhf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im52anpya2p1d3N1d21ma2xwamhmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTQ1MzA1MywiZXhwIjoyMDY1MDI5MDUzfQ.VwUJu4DnPcC3YGCD0dfFEXiq3dDq7wMMXu7-HeSNjCQ';

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugCollections() {
  console.log('🔍 Debugging Collections Functionality...\n');

  try {
    // 1. Check if collections table exists
    console.log('1. Checking collections table...');
    const { data: collections, error: collectionsError } = await supabase
      .from('collections')
      .select('*')
      .limit(5);

    if (collectionsError) {
      console.error('❌ Collections table error:', collectionsError);
    } else {
      console.log('✅ Collections table exists');
      console.log(`   Found ${collections.length} collections`);
      if (collections.length > 0) {
        console.log('   Sample collection:', collections[0]);
      }
    }

    // 2. Check if collection_recipes table exists
    console.log('\n2. Checking collection_recipes table...');
    const { data: collectionRecipes, error: collectionRecipesError } = await supabase
      .from('collection_recipes')
      .select('*')
      .limit(5);

    if (collectionRecipesError) {
      console.error('❌ Collection_recipes table error:', collectionRecipesError);
    } else {
      console.log('✅ Collection_recipes table exists');
      console.log(`   Found ${collectionRecipes.length} collection-recipe relationships`);
      if (collectionRecipes.length > 0) {
        console.log('   Sample relationship:', collectionRecipes[0]);
      }
    }

    // 3. Check if recipes table exists
    console.log('\n3. Checking recipes table...');
    const { data: recipes, error: recipesError } = await supabase
      .from('recipes')
      .select('id, title')
      .limit(5);

    if (recipesError) {
      console.error('❌ Recipes table error:', recipesError);
    } else {
      console.log('✅ Recipes table exists');
      console.log(`   Found ${recipes.length} recipes`);
      if (recipes.length > 0) {
        console.log('   Sample recipe:', recipes[0]);
      }
    }

    // 4. Test creating a collection
    console.log('\n4. Testing collection creation...');
    // Use a proper UUID format
    const testUserUuid = 'a4bc81e3-58eb-4e5d-9127-8dbb96e54e0c'; // Use existing user UUID from sample
    const { data: newCollection, error: createError } = await supabase
      .from('collections')
      .insert({
        user_id: testUserUuid,
        name: 'Test Collection Debug',
        description: 'A test collection for debugging',
        is_public: false
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Failed to create test collection:', createError);
    } else {
      console.log('✅ Successfully created test collection:', newCollection);

      // 5. Test adding recipe to collection (if we have recipes)
      if (recipes && recipes.length > 0) {
        console.log('\n5. Testing adding recipe to collection...');
        const { data: collectionRecipe, error: addError } = await supabase
          .from('collection_recipes')
          .insert({
            collection_id: newCollection.id,
            recipe_id: recipes[0].id
          })
          .select()
          .single();

        if (addError) {
          console.error('❌ Failed to add recipe to collection:', addError);
        } else {
          console.log('✅ Successfully added recipe to collection:', collectionRecipe);
        }
      }

      // Clean up test data
      console.log('\n6. Cleaning up test data...');
      await supabase.from('collections').delete().eq('id', newCollection.id);
      console.log('✅ Test data cleaned up');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

debugCollections();
