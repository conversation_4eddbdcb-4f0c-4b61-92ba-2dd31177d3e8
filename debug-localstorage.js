// Debug script to check localStorage for corrupted JSON data
console.log('🔍 Checking localStorage for corrupted JSON data...\n');

const keysToCheck = ['userMappings', 'savedRecipes'];

keysToCheck.forEach(key => {
  console.log(`\n--- Checking ${key} ---`);
  
  const rawValue = localStorage.getItem(key);
  console.log(`Raw value: "${rawValue}"`);
  
  if (rawValue === null) {
    console.log('✅ Key not found in localStorage');
    return;
  }
  
  if (rawValue === '') {
    console.log('⚠️ Empty string found');
    return;
  }
  
  try {
    const parsed = JSON.parse(rawValue);
    console.log('✅ JSON parsing successful');
    console.log('Parsed value:', parsed);
  } catch (err) {
    console.error('❌ JSON parsing failed:', err.message);
    console.log('This could be causing the error!');
    
    // Show character codes for debugging
    console.log('Character codes:', Array.from(rawValue).map((char, i) => `${i}: "${char}" (${char.charCodeAt(0)})`).slice(0, 10));
    
    // Clean up corrupted data
    console.log(`🧹 Cleaning up corrupted ${key}...`);
    if (key === 'userMappings') {
      localStorage.setItem(key, '{}');
    } else if (key === 'savedRecipes') {
      localStorage.setItem(key, '[]');
    }
    console.log('✅ Cleaned up');
  }
});

console.log('\n🔍 Checking all localStorage keys for potential issues...');
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  const value = localStorage.getItem(key);
  
  if (value && value.startsWith('{') || value.startsWith('[')) {
    try {
      JSON.parse(value);
    } catch (err) {
      console.error(`❌ Corrupted JSON in key "${key}":`, err.message);
      console.log(`Value: "${value.substring(0, 50)}..."`);
    }
  }
}

console.log('\n✅ localStorage check complete!');
